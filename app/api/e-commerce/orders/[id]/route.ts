// Individual Order API Routes
// GET /api/e-commerce/orders/[id] - Get order by ID
// PUT /api/e-commerce/orders/[id] - Update order

import { NextRequest, NextResponse } from 'next/server'
import { orderService, authService, handleEcommerceError } from '@/lib/ecommerce'
import { UpdateOrderInput } from '@/lib/ecommerce/types'

// Get service instances
const orderServiceInstance = orderService()
const authServiceInstance = authService()

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check if this is an admin request
    const isAdminRequest = request.headers.get('x-admin-request') === 'true' ||
                          request.nextUrl.pathname.includes('/admin/')

    let currentUser = null
    let isAdmin = false

    if (!isAdminRequest) {
      // Get token from cookie or header for customer requests
      const token = request.cookies.get('auth-token')?.value ||
                    request.headers.get('authorization')?.replace('Bearer ', '')

      if (!token) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        )
      }

      // Get current user
      const userResult = await authServiceInstance.getCurrentUser(token)
      if (!userResult.success || !userResult.data) {
        return NextResponse.json(
          { success: false, error: 'Invalid authentication token' },
          { status: 401 }
        )
      }

      currentUser = userResult.data
      isAdmin = currentUser.role === 'admin' || currentUser.isAdmin
    } else {
      // For admin requests, assume admin privileges
      isAdmin = true
    }

    const { id } = params

    const order = await orderServiceInstance.getOrder(id)

    if (!order) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      )
    }

    // Check if user owns this order (unless admin)
    if (!isAdmin && order.customerId !== currentUser?.id) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      data: order
    })
  } catch (error) {
    console.error('Get order API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

// PATCH method for partial updates (preferred for admin)
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  return handleOrderUpdate(request, { params })
}

// PUT method for full updates (backward compatibility)
export async function PUT(request: NextRequest, { params }: RouteParams) {
  return handleOrderUpdate(request, { params })
}

async function handleOrderUpdate(request: NextRequest, { params }: RouteParams) {
  try {
    // Check if this is an admin request
    const isAdminRequest = request.headers.get('x-admin-request') === 'true' ||
                          request.nextUrl.pathname.includes('/admin/')

    let currentUser = null
    let isAdmin = false

    if (!isAdminRequest) {
      // Get token from cookie or header for customer requests
      const token = request.cookies.get('auth-token')?.value ||
                    request.headers.get('authorization')?.replace('Bearer ', '')

      if (!token) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        )
      }

      // Get current user
      const userResult = await authServiceInstance.getCurrentUser(token)
      if (!userResult.success || !userResult.data) {
        return NextResponse.json(
          { success: false, error: 'Invalid authentication token' },
          { status: 401 }
        )
      }

      currentUser = userResult.data
      isAdmin = currentUser.role === 'admin' || currentUser.isAdmin
    } else {
      // For admin requests, assume admin privileges
      isAdmin = true
    }

    const { id } = params
    const body = await request.json()

    // First check if order exists and user owns it
    const existingOrder = await orderServiceInstance.getOrder(id)
    if (!existingOrder) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      )
    }

    // Check if user owns this order (unless admin)
    if (!isAdmin && existingOrder.customerId !== currentUser?.id) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      )
    }

    // Only allow limited updates for customers (mostly notes)
    const updateData = {
      customerNote: body.customerNote,
      // Admin-only fields would be restricted here
      ...(body.isAdmin && {
        status: body.status,
        paymentStatus: body.paymentStatus,
        fulfillmentStatus: body.fulfillmentStatus,
        internalNotes: body.internalNotes,
        tags: body.tags,
        attributes: body.attributes
      })
    }

    const updatedOrder = await orderServiceInstance.updateOrder(id, updateData)

    if (updatedOrder) {
      return NextResponse.json({
        success: true,
        data: updatedOrder
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to update order' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Update order API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
