'use client'

import { useState, useEffect, useMemo, use<PERSON><PERSON>back } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Search,
  Plus,
  Filter,
  MoreHorizontal,
  Edit,
  Copy,
  Trash2,
  Eye,
  Package,
  TrendingUp,
  AlertTriangle,
  Loader2,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Download,
  Upload,
  Settings,
  RefreshCw,
  Star,
  ShoppingCart,
  DollarSign,
  BarChart3,
  Calendar,
  Tag,
  Image as ImageIcon,
  Archive,
  CheckCircle2,
  XCircle,
  Clock,
  Zap
} from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuCheckboxItem, DropdownMenuLabel } from '@/components/ui/dropdown-menu'
import { useProducts, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { useCategories } from '@/lib/ecommerce/hooks/use-categories'
import { BulkProductOperations } from './bulk-product-operations'
import { formatCurrency } from '@/lib/utils'
import type { Product } from '@/lib/ecommerce/types'

type ViewMode = 'table' | 'grid' | 'compact'
type FilterPreset = 'all' | 'active' | 'low-stock' | 'out-of-stock' | 'draft' | 'recent'

interface EnhancedProductListProps {
  onCreateProduct: () => void
  onEditProduct: (product: Product) => void
  onViewProduct: (product: Product) => void
}

interface ProductFilters {
  status: ('active' | 'draft' | 'archived')[]
  categories: string[]
  priceRange: { min: number; max: number } | null
  stockStatus: 'all' | 'in-stock' | 'low-stock' | 'out-of-stock'
  dateRange: { from: Date; to: Date } | null
}

export function EnhancedProductList({
  onCreateProduct,
  onEditProduct,
  onViewProduct
}: EnhancedProductListProps) {
  // View and layout state
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [showFilters, setShowFilters] = useState(false)
  const [filterPreset, setFilterPreset] = useState<FilterPreset>('all')

  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [filters, setFilters] = useState<ProductFilters>({
    status: [],
    categories: [],
    priceRange: null,
    stockStatus: 'all',
    dateRange: null
  })

  // Selection and bulk operations
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [showBulkOperations, setShowBulkOperations] = useState(false)

  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [sortField, setSortField] = useState<'title' | 'price' | 'createdAt' | 'updatedAt' | 'inventoryQuantity' | 'averageRating'>('createdAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  // UI state
  const [isRefreshing, setIsRefreshing] = useState(false)

  const { categories } = useCategories()
  const {
    duplicateProduct,
    deleteProduct,
    loading: mutationLoading,
    error: mutationError,
    clearError
  } = useProductMutations()

  // Debounce search query
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearch(searchQuery)
    }, 300)
    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  // Build search parameters using useMemo to prevent unnecessary re-renders
  const searchParams = useMemo(() => ({
    query: debouncedSearch || undefined,
    filters: {
      status: filters.status.length > 0 ? filters.status : undefined,
      categoryIds: filters.categories.length > 0 ? filters.categories : undefined,
      priceRange: filters.priceRange || undefined,
      inStock: filters.stockStatus === 'in-stock' ? true :
               filters.stockStatus === 'out-of-stock' ? false : undefined
    },
    sort: {
      field: sortField,
      direction: sortDirection
    },
    page: currentPage,
    limit: pageSize
  }), [debouncedSearch, filters, sortField, sortDirection, currentPage, pageSize])

  const { products, loading, error, pagination, refetch, searchProducts } = useProducts({
    initialParams: searchParams
  })

  // Update search when parameters change
  useEffect(() => {
    searchProducts(searchParams)
  }, [searchParams, searchProducts])

  // Filter presets
  const applyFilterPreset = useCallback((preset: FilterPreset) => {
    setFilterPreset(preset)
    setCurrentPage(1)

    switch (preset) {
      case 'all':
        setFilters({
          status: [],
          categories: [],
          priceRange: null,
          stockStatus: 'all',
          dateRange: null
        })
        break
      case 'active':
        setFilters(prev => ({ ...prev, status: ['active'] }))
        break
      case 'low-stock':
        setFilters(prev => ({ ...prev, stockStatus: 'low-stock' }))
        break
      case 'out-of-stock':
        setFilters(prev => ({ ...prev, stockStatus: 'out-of-stock' }))
        break
      case 'draft':
        setFilters(prev => ({ ...prev, status: ['draft'] }))
        break
      case 'recent':
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        setFilters(prev => ({
          ...prev,
          dateRange: { from: weekAgo, to: new Date() }
        }))
        break
    }
  }, [])

  // Enhanced refresh with loading state
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    try {
      await refetch()
    } finally {
      setIsRefreshing(false)
    }
  }, [refetch])

  // Handle product selection
  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId])
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(products.map(p => p.id))
    } else {
      setSelectedProducts([])
    }
  }

  // Handle product actions
  const handleDuplicateProduct = async (product: Product) => {
    const duplicated = await duplicateProduct(product.id)
    if (duplicated) {
      refetch()
    }
  }

  const handleDeleteProduct = async (product: Product) => {
    if (confirm(`Are you sure you want to delete "${product.title}"?`)) {
      const deleted = await deleteProduct(product.id)
      if (deleted) {
        refetch()
      }
    }
  }

  // Status badge styling
  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'default',
      draft: 'secondary',
      archived: 'outline'
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  // Stock status indicator
  const getStockIndicator = (product: Product) => {
    if (!product.trackQuantity) {
      return <Badge variant="outline">Not tracked</Badge>
    }

    if (product.inventoryQuantity <= 0) {
      return <Badge variant="destructive">Out of stock</Badge>
    }

    if (product.inventoryQuantity <= 5) {
      return <Badge variant="secondary">Low stock</Badge>
    }

    return <Badge variant="default">In stock</Badge>
  }

  // Statistics calculations
  const stats = useMemo(() => {
    const totalProducts = pagination?.total || 0
    const activeProducts = products.filter(p => p.status === 'active').length
    const lowStockProducts = products.filter(p => p.inventoryQuantity <= 10).length
    const outOfStockProducts = products.filter(p => p.inventoryQuantity === 0).length
    const totalValue = products.reduce((sum, p) => sum + (p.price.amount * p.inventoryQuantity), 0)

    return {
      total: totalProducts,
      active: activeProducts,
      lowStock: lowStockProducts,
      outOfStock: outOfStockProducts,
      totalValue
    }
  }, [products, pagination])

  return (
    <div className="space-y-6">
      {/* Filter Presets and Advanced Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Filters & Search
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Settings className="mr-2 h-4 w-4" />
                Advanced
              </Button>
              <div className="flex items-center space-x-1">
                <Button
                  variant={viewMode === 'table' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Quick Filter Presets */}
          <div className="flex flex-wrap gap-2 mb-4">
            {(['all', 'active', 'low-stock', 'out-of-stock', 'draft', 'recent'] as FilterPreset[]).map((preset) => (
              <Button
                key={preset}
                variant={filterPreset === preset ? 'default' : 'outline'}
                size="sm"
                onClick={() => applyFilterPreset(preset)}
              >
                {preset === 'all' && <Package className="mr-2 h-3 w-3" />}
                {preset === 'active' && <CheckCircle2 className="mr-2 h-3 w-3" />}
                {preset === 'low-stock' && <AlertTriangle className="mr-2 h-3 w-3" />}
                {preset === 'out-of-stock' && <XCircle className="mr-2 h-3 w-3" />}
                {preset === 'draft' && <Clock className="mr-2 h-3 w-3" />}
                {preset === 'recent' && <Calendar className="mr-2 h-3 w-3" />}
                {preset.charAt(0).toUpperCase() + preset.slice(1).replace('-', ' ')}
              </Button>
            ))}
          </div>

          {/* Search and Basic Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={filters.status.length === 1 ? filters.status[0] : 'all'}
              onValueChange={(value) => {
                if (value === 'all') {
                  setFilters(prev => ({ ...prev, status: [] }))
                } else {
                  setFilters(prev => ({ ...prev, status: [value as 'active' | 'draft' | 'archived'] }))
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.categories.length === 1 ? filters.categories[0] : 'all'}
              onValueChange={(value) => {
                if (value === 'all') {
                  setFilters(prev => ({ ...prev, categories: [] }))
                } else {
                  setFilters(prev => ({ ...prev, categories: [value] }))
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={`${sortField}-${sortDirection}`} onValueChange={(value) => {
              const [field, direction] = value.split('-')
              setSortField(field as 'title' | 'price' | 'createdAt' | 'updatedAt' | 'inventoryQuantity' | 'averageRating')
              setSortDirection(direction as 'asc' | 'desc')
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Newest first
                  </div>
                </SelectItem>
                <SelectItem value="createdAt-asc">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Oldest first
                  </div>
                </SelectItem>
                <SelectItem value="title-asc">
                  <div className="flex items-center">
                    <SortAsc className="mr-2 h-4 w-4" />
                    Name A-Z
                  </div>
                </SelectItem>
                <SelectItem value="title-desc">
                  <div className="flex items-center">
                    <SortDesc className="mr-2 h-4 w-4" />
                    Name Z-A
                  </div>
                </SelectItem>
                <SelectItem value="price-asc">
                  <div className="flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Price low to high
                  </div>
                </SelectItem>
                <SelectItem value="price-desc">
                  <div className="flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Price high to low
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Operations */}
      {selectedProducts.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Package className="h-4 w-4" />
                <span className="font-medium">
                  {selectedProducts.length} product{selectedProducts.length !== 1 ? 's' : ''} selected
                </span>
              </div>
              <Button
                variant="outline"
                onClick={() => setShowBulkOperations(!showBulkOperations)}
              >
                Bulk Actions
              </Button>
            </div>
            {showBulkOperations && (
              <div className="mt-4">
                <BulkProductOperations
                  selectedProducts={selectedProducts}
                  products={products.map(p => ({
                    id: p.id,
                    title: p.title,
                    status: p.status,
                    price: p.price.amount
                  }))}
                  onOperationComplete={() => {
                    setSelectedProducts([])
                    setShowBulkOperations(false)
                    refetch()
                  }}
                  onClearSelection={() => {
                    setSelectedProducts([])
                    setShowBulkOperations(false)
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {(error || mutationError) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error?.message || mutationError?.message || 'An error occurred'}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={() => {
                clearError()
                refetch()
              }}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Enhanced Products Display */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                Products
                {selectedProducts.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {selectedProducts.length} selected
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {pagination ? (
                  <div className="flex items-center space-x-4">
                    <span>{pagination.total} total products</span>
                    <Separator orientation="vertical" className="h-4" />
                    <span>Page {pagination.page} of {pagination.totalPages}</span>
                    <Separator orientation="vertical" className="h-4" />
                    <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                    <span>per page</span>
                  </div>
                ) : 'Loading products...'}
              </CardDescription>
            </div>
            {selectedProducts.length > 0 && (
              <Button
                variant="outline"
                onClick={() => setShowBulkOperations(!showBulkOperations)}
              >
                <Zap className="mr-2 h-4 w-4" />
                Bulk Actions ({selectedProducts.length})
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">Loading products...</p>
              </div>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-12">
              <Package className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No products found</h3>
              <p className="text-sm text-muted-foreground mb-6">
                {searchQuery || filters.status.length > 0 || filters.categories.length > 0
                  ? 'Try adjusting your search or filters to find what you\'re looking for.'
                  : 'Get started by creating your first product.'}
              </p>
              <div className="flex items-center justify-center space-x-2">
                {(searchQuery || filters.status.length > 0 || filters.categories.length > 0) && (
                  <Button variant="outline" onClick={() => {
                    setSearchQuery('')
                    setFilters({
                      status: [],
                      categories: [],
                      priceRange: null,
                      stockStatus: 'all',
                      dateRange: null
                    })
                    setFilterPreset('all')
                  }}>
                    Clear Filters
                  </Button>
                )}
                <Button onClick={onCreateProduct}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Product
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {viewMode === 'table' ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedProducts.length === products.length}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>Product</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Stock</TableHead>
                        <TableHead>Price</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Rating</TableHead>
                        <TableHead>Updated</TableHead>
                        <TableHead className="w-12"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {products.map((product) => (
                        <TableRow key={product.id} className="group hover:bg-muted/50">
                          <TableCell>
                            <Checkbox
                              checked={selectedProducts.includes(product.id)}
                              onCheckedChange={(checked) =>
                                handleSelectProduct(product.id, checked as boolean)
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              {product.images?.[0] ? (
                                <img
                                  src={product.images[0].url}
                                  alt={product.images[0].altText}
                                  className="h-12 w-12 rounded-lg object-cover border"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    target.nextElementSibling?.classList.remove('hidden');
                                  }}
                                />
                              ) : null}
                              <div className={`h-12 w-12 rounded-lg bg-muted flex items-center justify-center border ${product.images?.[0] ? 'hidden' : ''}`}>
                                <Package className="h-6 w-6 text-muted-foreground" />
                              </div>
                              <div className="min-w-0 flex-1">
                                <div className="font-medium truncate">{product.title}</div>
                                <div className="text-sm text-muted-foreground truncate">
                                  {product.vendor || 'No vendor'}
                                </div>
                                {product.description && (
                                  <div className="text-xs text-muted-foreground truncate max-w-xs">
                                    {product.description}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{getStatusBadge(product.status)}</TableCell>
                          <TableCell>{getStockIndicator(product)}</TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {formatCurrency(product.price.amount, product.price.currency)}
                              </div>
                              {product.compareAtPrice && (
                                <div className="text-sm text-muted-foreground line-through">
                                  {formatCurrency(product.compareAtPrice.amount, product.compareAtPrice.currency)}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {product.categories?.slice(0, 2).map((category) => (
                                <Badge key={category.id} variant="outline" className="text-xs">
                                  {category.name}
                                </Badge>
                              ))}
                              {product.categories && product.categories.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{product.categories.length - 2}
                                </Badge>
                              )}
                              {!product.categories?.length && (
                                <span className="text-sm text-muted-foreground">-</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {product.averageRating ? (
                              <div className="flex items-center space-x-1">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                <span className="text-sm font-medium">{product.averageRating.toFixed(1)}</span>
                                <span className="text-xs text-muted-foreground">({product.reviewCount})</span>
                              </div>
                            ) : (
                              <span className="text-sm text-muted-foreground">No reviews</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-muted-foreground">
                              {new Date(product.updatedAt).toLocaleDateString()}
                            </div>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => onViewProduct(product)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => onEditProduct(product)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDuplicateProduct(product)}>
                                  <Copy className="mr-2 h-4 w-4" />
                                  Duplicate
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteProduct(product)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {products.map((product) => (
                    <Card key={product.id} className="group hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          {/* Product Image */}
                          <div className="relative">
                            <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                              {product.images?.[0] ? (
                                <img
                                  src={product.images[0].url}
                                  alt={product.images[0].altText}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    target.nextElementSibling?.classList.remove('hidden');
                                  }}
                                />
                              ) : null}
                              <div className={`w-full h-full flex items-center justify-center ${product.images?.[0] ? 'hidden' : ''}`}>
                                <Package className="h-12 w-12 text-muted-foreground" />
                              </div>
                            </div>

                            {/* Selection Checkbox */}
                            <div className="absolute top-2 left-2">
                              <Checkbox
                                checked={selectedProducts.includes(product.id)}
                                onCheckedChange={(checked) =>
                                  handleSelectProduct(product.id, checked as boolean)
                                }
                                className="bg-white/80 backdrop-blur-sm"
                              />
                            </div>

                            {/* Quick Actions */}
                            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="secondary" size="sm" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => onViewProduct(product)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => onEditProduct(product)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleDuplicateProduct(product)}>
                                    <Copy className="mr-2 h-4 w-4" />
                                    Duplicate
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => handleDeleteProduct(product)}
                                    className="text-red-600"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>

                            {/* Status Badge */}
                            <div className="absolute bottom-2 left-2">
                              {getStatusBadge(product.status)}
                            </div>
                          </div>

                          {/* Product Info */}
                          <div className="space-y-2">
                            <div>
                              <h3 className="font-medium truncate" title={product.title}>
                                {product.title}
                              </h3>
                              {product.vendor && (
                                <p className="text-sm text-muted-foreground truncate">
                                  {product.vendor}
                                </p>
                              )}
                            </div>

                            {/* Price */}
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-semibold">
                                  {formatCurrency(product.price.amount, product.price.currency)}
                                </div>
                                {product.compareAtPrice && (
                                  <div className="text-sm text-muted-foreground line-through">
                                    {formatCurrency(product.compareAtPrice.amount, product.compareAtPrice.currency)}
                                  </div>
                                )}
                              </div>
                              {getStockIndicator(product)}
                            </div>

                            {/* Rating */}
                            {product.averageRating ? (
                              <div className="flex items-center space-x-1">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                <span className="text-sm font-medium">{product.averageRating.toFixed(1)}</span>
                                <span className="text-xs text-muted-foreground">({product.reviewCount})</span>
                              </div>
                            ) : null}

                            {/* Categories */}
                            {product.categories && product.categories.length > 0 && (
                              <div className="flex flex-wrap gap-1">
                                {product.categories.slice(0, 2).map((category) => (
                                  <Badge key={category.id} variant="outline" className="text-xs">
                                    {category.name}
                                  </Badge>
                                ))}
                                {product.categories.length > 2 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{product.categories.length - 2}
                                  </Badge>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Enhanced Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-4 border-t">
                  <div className="text-sm text-muted-foreground">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} products
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(1)}
                      disabled={pagination.page === 1}
                    >
                      First
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.page - 1)}
                      disabled={!pagination.hasPrev}
                    >
                      Previous
                    </Button>

                    {/* Page Numbers */}
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        let pageNum
                        if (pagination.totalPages <= 5) {
                          pageNum = i + 1
                        } else if (pagination.page <= 3) {
                          pageNum = i + 1
                        } else if (pagination.page >= pagination.totalPages - 2) {
                          pageNum = pagination.totalPages - 4 + i
                        } else {
                          pageNum = pagination.page - 2 + i
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === pagination.page ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(pageNum)}
                            className="w-8 h-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        )
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.page + 1)}
                      disabled={!pagination.hasNext}
                    >
                      Next
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(pagination.totalPages)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Last
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
